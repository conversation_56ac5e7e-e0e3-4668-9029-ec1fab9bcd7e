# Production docker-compose configuration
# Usage: docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

version: '3.8'

services:
  web:
    # Production environment variables
    environment:
      - DEBUG=False
      - SECURE_SSL_REDIRECT=True
      - SECURE_HSTS_SECONDS=31536000
      - SECURE_HSTS_INCLUDE_SUBDOMAINS=True
      - SECURE_HSTS_PRELOAD=True
      - SECURE_CONTENT_TYPE_NOSNIFF=True
      - SECURE_BROWSER_XSS_FILTER=True
      - SESSION_COOKIE_SECURE=True
      - CSRF_COOKIE_SECURE=True
    # Resource limits
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'
    # Logging configuration
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    # Restart policy
    restart: unless-stopped

  db:
    # Don't expose database port in production
    ports: []
    # Resource limits
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.3'
        reservations:
          memory: 256M
          cpus: '0.1'
    # Production database configuration
    environment:
      - POSTGRES_SHARED_PRELOAD_LIBRARIES=pg_stat_statements
      - POSTGRES_MAX_CONNECTIONS=100
      - POSTGRES_SHARED_BUFFERS=128MB
    # Logging configuration
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  redis:
    # Don't expose Redis port in production
    ports: []
    # Production Redis configuration
    command: redis-server --maxmemory 128mb --maxmemory-policy allkeys-lru --save 900 1 --save 300 10
    # Resource limits
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.2'
        reservations:
          memory: 128M
          cpus: '0.1'
    # Logging configuration
    logging:
      driver: "json-file"
      options:
        max-size: "5m"
        max-file: "3"

  nginx:
    # Resource limits
    deploy:
      resources:
        limits:
          memory: 128M
          cpus: '0.2'
        reservations:
          memory: 64M
          cpus: '0.1'
    # Logging configuration
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
