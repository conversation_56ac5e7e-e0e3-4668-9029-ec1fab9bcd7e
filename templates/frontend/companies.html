{% extends 'base.html' %}

{% block title %}Companies Management - Valet Parking System{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1><i class="fas fa-building"></i> Companies Management</h1>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCompanyModal">
                    <i class="fas fa-plus"></i> Add New Company
                </button>
            </div>
        </div>
    </div>
    
    <div id="alerts"></div>
    
    <!-- Companies List -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-list"></i> Companies</h5>
                </div>
                <div class="card-body">
                    <div id="companiesLoading" class="loading">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover" id="companiesTable" style="display: none;">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Code</th>
                                    <th>Phone</th>
                                    <th>Location</th>
                                    <th>Admin</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="companiesBody">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Company Modal -->
<div class="modal fade" id="addCompanyModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-plus"></i> Add New Company</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addCompanyForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="companyName" class="form-label">Company Name</label>
                        <input type="text" class="form-control" id="companyName" name="name" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="companyPhone" class="form-label">Phone Number</label>
                        <input type="tel" class="form-control" id="companyPhone" name="phone_number" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="companyLocation" class="form-label">Location</label>
                        <textarea class="form-control" id="companyLocation" name="location" rows="3" required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Create Company
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Company Details Modal -->
<div class="modal fade" id="companyDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-building"></i> Company Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="companyDetailsContent">
                <!-- Content will be loaded dynamically -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="editCompanyBtn">
                    <i class="fas fa-edit"></i> Edit Company
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let allCompanies = [];

async function loadCompanies() {
    try {
        document.getElementById('companiesLoading').style.display = 'block';
        document.getElementById('companiesTable').style.display = 'none';
        
        const companies = await apiRequest('/api/companies/');
        allCompanies = companies;
        renderCompanies(companies);
        
        document.getElementById('companiesLoading').style.display = 'none';
        document.getElementById('companiesTable').style.display = 'table';
    } catch (error) {
        console.error('Failed to load companies:', error);
        showAlert('Failed to load companies', 'danger');
        document.getElementById('companiesLoading').style.display = 'none';
    }
}

function renderCompanies(companies) {
    const tbody = document.getElementById('companiesBody');
    
    if (companies.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center text-muted py-4">
                    <i class="fas fa-building fa-3x mb-3"></i><br>
                    No companies found
                </td>
            </tr>
        `;
        return;
    }
    
    const companiesHTML = companies.map(company => `
        <tr>
            <td><strong>${company.name}</strong></td>
            <td><code>${company.company_code}</code></td>
            <td>${company.phone_number}</td>
            <td>${company.location}</td>
            <td>${company.admin_user ? company.admin_user.username : 'N/A'}</td>
            <td>${new Date(company.created_at).toLocaleDateString()}</td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-primary" onclick="viewCompanyDetails(${company.id})">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-outline-info" onclick="manageEmployees(${company.id})">
                        <i class="fas fa-users"></i>
                    </button>
                    <button class="btn btn-outline-danger" onclick="deleteCompany(${company.id})">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
    
    tbody.innerHTML = companiesHTML;
}

function viewCompanyDetails(companyId) {
    const company = allCompanies.find(c => c.id === companyId);
    if (!company) return;
    
    const content = `
        <div class="row">
            <div class="col-md-6">
                <h6>Company Information</h6>
                <table class="table table-sm">
                    <tr><td><strong>Name:</strong></td><td>${company.name}</td></tr>
                    <tr><td><strong>Code:</strong></td><td><code>${company.company_code}</code></td></tr>
                    <tr><td><strong>Phone:</strong></td><td>${company.phone_number}</td></tr>
                    <tr><td><strong>Location:</strong></td><td>${company.location}</td></tr>
                    <tr><td><strong>Created:</strong></td><td>${new Date(company.created_at).toLocaleString()}</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>Admin User</h6>
                ${company.admin_user ? `
                    <table class="table table-sm">
                        <tr><td><strong>Username:</strong></td><td>${company.admin_user.username}</td></tr>
                        <tr><td><strong>Email:</strong></td><td>${company.admin_user.email || 'N/A'}</td></tr>
                        <tr><td><strong>Name:</strong></td><td>${company.admin_user.first_name} ${company.admin_user.last_name}</td></tr>
                        <tr><td><strong>Role:</strong></td><td>${company.admin_user.role}</td></tr>
                    </table>
                ` : '<p class="text-muted">No admin user assigned</p>'}
            </div>
        </div>
    `;
    
    document.getElementById('companyDetailsContent').innerHTML = content;
    new bootstrap.Modal(document.getElementById('companyDetailsModal')).show();
}

function manageEmployees(companyId) {
    showAlert('Employee management feature coming soon!', 'info');
}

function deleteCompany(companyId) {
    if (confirm('Are you sure you want to delete this company? This action cannot be undone.')) {
        apiRequest(`/api/companies/${companyId}/`, {
            method: 'DELETE',
        })
        .then(() => {
            showAlert('Company deleted successfully!', 'success');
            loadCompanies();
        })
        .catch(() => {
            showAlert('Failed to delete company', 'danger');
        });
    }
}

// Form submission
document.getElementById('addCompanyForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const companyData = Object.fromEntries(formData);
    
    try {
        await apiRequest('/api/companies/', {
            method: 'POST',
            body: JSON.stringify(companyData),
        });
        
        showAlert('Company created successfully!', 'success');
        bootstrap.Modal.getInstance(document.getElementById('addCompanyModal')).hide();
        e.target.reset();
        loadCompanies();
    } catch (error) {
        showAlert('Failed to create company', 'danger');
    }
});

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    loadCompanies();
});
</script>
{% endblock %}
