{% extends 'base.html' %}

{% block title %}Join as Employee - Valet Parking System{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center" style="min-height: 80vh; align-items: center;">
        <div class="col-md-8 col-lg-6">
            <div class="form-container">
                <div class="text-center mb-4">
                    <i class="fas fa-user text-primary" style="font-size: 3rem;"></i>
                    <h2 class="mt-3">Join as Employee</h2>
                    <p class="text-muted">Join an existing company with your company code</p>
                </div>
                
                <div id="alerts"></div>
                
                <form id="registerEmployeeForm">
                    {% csrf_token %}
                    <!-- Company Code -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-key"></i> Company Code</h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="companyCode" class="form-label">Company Code *</label>
                                <input type="text" class="form-control" id="companyCode" name="company_code" required placeholder="Enter the code provided by your company">
                                <div class="form-text">Ask your company admin for this code</div>
                            </div>
                            <button type="button" class="btn btn-outline-primary" onclick="verifyCompanyCode()">
                                <i class="fas fa-search"></i> Verify Company
                            </button>
                            <div id="companyInfo" class="mt-3" style="display: none;">
                                <div class="alert alert-success">
                                    <h6><i class="fas fa-check-circle"></i> Company Found!</h6>
                                    <p class="mb-0" id="companyDetails"></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Personal Information -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-user"></i> Personal Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="firstName" class="form-label">First Name *</label>
                                        <input type="text" class="form-control" id="firstName" name="first_name" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="lastName" class="form-label">Last Name *</label>
                                        <input type="text" class="form-control" id="lastName" name="last_name" required>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="username" class="form-label">Username *</label>
                                <input type="text" class="form-control" id="username" name="username" required>
                                <div class="form-text">This will be your login username</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="email" class="form-label">Email Address *</label>
                                <input type="email" class="form-control" id="email" name="email" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="phone" class="form-label">Phone Number *</label>
                                <input type="tel" class="form-control" id="phone" name="phone_number" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="password" class="form-label">Password *</label>
                                <input type="password" class="form-control" id="password" name="password" required>
                                <div class="form-text">Minimum 8 characters</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="confirmPassword" class="form-label">Confirm Password *</label>
                                <input type="password" class="form-control" id="confirmPassword" name="confirm_password" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="agreeTerms" required>
                        <label class="form-check-label" for="agreeTerms">
                            I agree to the <a href="#" class="text-primary">Terms of Service</a> and <a href="#" class="text-primary">Privacy Policy</a>
                        </label>
                    </div>
                    
                    <button type="submit" class="btn btn-primary w-100 mb-3">
                        <i class="fas fa-user-plus"></i> Join as Employee
                    </button>
                </form>
                
                <div class="text-center">
                    <p class="mb-0">Already have an account? 
                        <a href="{% url 'frontend:login' %}" class="text-primary">Sign in here</a>
                    </p>
                    <p class="mb-0">Want to register a company? 
                        <a href="{% url 'frontend:register_company' %}" class="text-primary">Register Company</a>
                    </p>
                </div>
                
                <hr class="my-4">
                
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle"></i> Employee Role</h6>
                    <p class="mb-0">As an employee, you can:</p>
                    <ul class="mb-0">
                        <li>Manage parking transactions (park/retrieve cars)</li>
                        <li>View parking slots and their status</li>
                        <li>Handle customer requests via the dashboard</li>
                        <li>Update transaction statuses</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let verifiedCompany = null;

async function verifyCompanyCode() {
    const companyCode = document.getElementById('companyCode').value.trim();
    if (!companyCode) {
        showAlert('Please enter a company code', 'warning');
        return;
    }
    
    try {
        const response = await fetch(`/api/auth/verify-company-code/?code=${encodeURIComponent(companyCode)}`);
        
        if (response.ok) {
            const company = await response.json();
            verifiedCompany = company;
            
            document.getElementById('companyDetails').innerHTML = `
                <strong>${company.name}</strong><br>
                Location: ${company.location}<br>
                Phone: ${company.phone_number}
            `;
            document.getElementById('companyInfo').style.display = 'block';
            showAlert('Company verified successfully!', 'success');
        } else {
            document.getElementById('companyInfo').style.display = 'none';
            verifiedCompany = null;
            showAlert('Invalid company code. Please check with your company admin.', 'danger');
        }
    } catch (error) {
        console.error('Verification error:', error);
        showAlert('Failed to verify company code. Please try again.', 'danger');
    }
}

document.getElementById('registerEmployeeForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    if (!verifiedCompany) {
        showAlert('Please verify your company code first', 'warning');
        return;
    }
    
    const formData = new FormData(e.target);
    const data = Object.fromEntries(formData);
    
    // Validate passwords match
    if (data.password !== data.confirm_password) {
        showAlert('Passwords do not match', 'danger');
        return;
    }
    
    // Remove confirm_password and add company_code
    delete data.confirm_password;
    
    // Show loading state
    const submitBtn = e.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Creating account...';
    submitBtn.disabled = true;
    
    try {
        const response = await fetch('/api/auth/register-employee/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            body: JSON.stringify(data)
        });
        
        if (response.ok) {
            showAlert('Employee account created successfully! You can now login.', 'success');
            setTimeout(() => {
                window.location.href = '/login/';
            }, 2000);
        } else {
            const error = await response.json();
            showAlert(error.message || 'Registration failed. Please try again.', 'danger');
        }
    } catch (error) {
        console.error('Registration error:', error);
        showAlert('Registration failed. Please try again.', 'danger');
    } finally {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }
});

// Real-time password validation
document.getElementById('confirmPassword').addEventListener('input', function() {
    const password = document.getElementById('password').value;
    const confirmPassword = this.value;
    
    if (confirmPassword && password !== confirmPassword) {
        this.setCustomValidity('Passwords do not match');
        this.classList.add('is-invalid');
    } else {
        this.setCustomValidity('');
        this.classList.remove('is-invalid');
    }
});

// Function to get CSRF token
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}
</script>
{% endblock %}
