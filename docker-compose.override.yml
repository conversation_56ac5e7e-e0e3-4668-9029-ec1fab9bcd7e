# Development overrides for docker-compose.yml
# This file is automatically loaded by docker-compose

version: '3.8'

services:
  web:
    # Enable debug mode for development
    environment:
      - DEBUG=True
    # Mount source code for live reloading
    volumes:
      - .:/app
      - static_volume:/app/staticfiles
      - media_volume:/app/media
      - ./logs:/app/logs
    # Override command for development server
    command: >
      sh -c "python manage.py wait_for_db &&
             python manage.py migrate &&
             python manage.py collectstatic --noinput &&
             python manage.py runserver 0.0.0.0:8000"

  # Expose database port for development tools
  db:
    ports:
      - "5432:5432"

  # Expose Redis port for development tools
  redis:
    ports:
      - "6379:6379"
