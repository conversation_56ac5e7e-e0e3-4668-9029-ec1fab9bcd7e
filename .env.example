# Copy this file to .env and update the values

# Django Settings
SECRET_KEY=your-super-secret-key-change-this-in-production
DEBUG=False
ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0,your-domain.com

# Database Settings
DB_NAME=valet_db
DB_USER=postgres
DB_PASSWORD=200899
DB_HOST=localhost
DB_PORT=5432

# Redis Settings
REDIS_URL=redis://redis:6379/1

# WhatsApp/Twilio Settings
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_WHATSAPP_NUMBER=+***********

# Email Settings (optional)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password

# Security Settings (for production)
SECURE_SSL_REDIRECT=False
SECURE_HSTS_SECONDS=********
SECURE_HSTS_INCLUDE_SUBDOMAINS=True
SECURE_HSTS_PRELOAD=True
SECURE_CONTENT_TYPE_NOSNIFF=True
SECURE_BROWSER_XSS_FILTER=True
SESSION_COOKIE_SECURE=False
CSRF_COOKIE_SECURE=False
