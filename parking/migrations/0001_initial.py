# Generated by Django 4.2.21 on 2025-05-31 22:42

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("companies", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Customer",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("phone_number", models.CharField(max_length=20, unique=True)),
                ("name", models.CharField(blank=True, max_length=255, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name="ParkingSlot",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.Char<PERSON><PERSON>(max_length=100)),
                ("division", models.Char<PERSON>ield(max_length=100)),
                (
                    "qr_code_image",
                    models.ImageField(blank=True, null=True, upload_to="qr_codes/"),
                ),
                ("is_active", models.BooleanField(default=True)),
                ("is_occupied", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                (
                    "company",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="parking_slots",
                        to="companies.company",
                    ),
                ),
            ],
            options={
                "unique_together": {("company", "name")},
            },
        ),
        migrations.CreateModel(
            name="ParkingTransaction",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "plate_number",
                    models.CharField(blank=True, max_length=50, null=True),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending_park", "Pending Park"),
                            ("parked", "Parked"),
                            ("pending_retrieve", "Pending Retrieve"),
                            ("delivered", "Delivered"),
                        ],
                        default="pending_park",
                        max_length=20,
                    ),
                ),
                ("requested_at", models.DateTimeField(auto_now_add=True)),
                ("parked_at", models.DateTimeField(blank=True, null=True)),
                ("retrieve_requested_at", models.DateTimeField(blank=True, null=True)),
                ("delivered_at", models.DateTimeField(blank=True, null=True)),
                ("raw_whatsapp_payload", models.JSONField(blank=True, null=True)),
                ("ticket_code", models.CharField(blank=True, max_length=50, null=True)),
                (
                    "customer",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="transactions",
                        to="parking.customer",
                    ),
                ),
                (
                    "employee_assigned",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="assigned_transactions",
                        to="companies.employeeprofile",
                    ),
                ),
                (
                    "slot",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="transactions",
                        to="parking.parkingslot",
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name="NotificationLog",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "direction",
                    models.CharField(
                        choices=[("incoming", "Incoming"), ("outgoing", "Outgoing")],
                        max_length=10,
                    ),
                ),
                (
                    "whatsapp_message_id",
                    models.CharField(blank=True, max_length=100, null=True),
                ),
                ("payload", models.JSONField()),
                ("timestamp", models.DateTimeField(auto_now_add=True)),
                (
                    "transaction",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="notification_logs",
                        to="parking.parkingtransaction",
                    ),
                ),
            ],
        ),
    ]
