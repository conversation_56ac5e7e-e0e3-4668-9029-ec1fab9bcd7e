version: '3.8'

services:
  # PostgreSQL Database
  db:
    image: postgres:13
    container_name: valet_parking_db
    restart: unless-stopped
    environment:
      POSTGRES_DB: valet_parking
      POSTGRES_USER: valet_user
      POSTGRES_PASSWORD: valet_password123
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U valet_user -d valet_parking"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis for caching and sessions (optional but recommended)
  redis:
    image: redis:7-alpine
    container_name: valet_parking_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Django Web Application
  web:
    build: .
    container_name: valet_parking_web
    restart: unless-stopped
    ports:
      - "8000:8000"
    environment:
      - DEBUG=False
      - SECRET_KEY=your-super-secret-key-change-this-in-production
      - DB_NAME=valet_parking
      - DB_USER=valet_user
      - DB_PASSWORD=valet_password123
      - DB_HOST=localhost
      - DB_PORT=5432
      - REDIS_URL=redis://redis:6379/1
      - ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0
      - TWILIO_ACCOUNT_SID=${TWILIO_ACCOUNT_SID:-}
      - TWILIO_AUTH_TOKEN=${TWILIO_AUTH_TOKEN:-}
      - TWILIO_WHATSAPP_NUMBER=${TWILIO_WHATSAPP_NUMBER:-}
    volumes:
      - static_volume:/app/staticfiles
      - media_volume:/app/media
      - ./logs:/app/logs
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/companies/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: valet_parking_nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/default.conf:/etc/nginx/conf.d/default.conf
      - static_volume:/app/staticfiles
      - media_volume:/app/media
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - web
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  postgres_data:
  redis_data:
  static_volume:
  media_volume:

networks:
  default:
    name: valet_parking_network
