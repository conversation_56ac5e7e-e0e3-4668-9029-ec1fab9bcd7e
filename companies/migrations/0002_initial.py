# Generated by Django 4.2.21 on 2025-05-31 22:42

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ("companies", "0001_initial"),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="employeeprofile",
            name="user",
            field=models.OneToOneField(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="employee_profile",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="company",
            name="admin_user",
            field=models.OneToOneField(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="company_admin_profile",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
    ]
