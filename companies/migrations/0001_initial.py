# Generated by Django 4.2.21 on 2025-05-31 22:42

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name="Company",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("name", models.Char<PERSON>ield(max_length=255, unique=True)),
                ("phone_number", models.CharField(max_length=20)),
                ("location", models.CharField(max_length=255)),
                ("company_code", models.CharField(max_length=50, unique=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name="EmployeeProfile",
            fields=[
                ("id", models.BigAutoField(primary_key=True, serialize=False)),
                ("phone_number", models.<PERSON><PERSON><PERSON><PERSON>(max_length=20)),
                ("other_info", models.J<PERSON><PERSON>ield(blank=True, null=True)),
                (
                    "company",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="employees",
                        to="companies.company",
                    ),
                ),
            ],
        ),
    ]
